public with sharing class CCM_PayPalUtil {
    public static AuthorizeResponse authorizePayPalPayment(String CardNum, String ExpDate, String cvv2, Decimal Amount, String OrderCode, BillingAddress billToAddress, String accountName, String caseName) {
        String mdtName = 'PayPal_Sandbox';
        String orgId = UserInfo.getOrganizationId();
        Organization org = [SELECT Id, IsSandbox FROM Organization WHERE Id = :orgId];
        if (!org.IsSandbox) {
            mdtName = 'PayPal_Production';
        }
        PayPal_Configuration__mdt config = [SELECT End_Point__c, PayPal_User__c, PayPal_Vender__c,User_credentia__c,Partner__c
                                           FROM PayPal_Configuration__mdt
                                           WHERE DeveloperName = :mdtName LIMIT 1];

        Map<String, String> paramMap = new Map<String, String>();
        paramMap.put('TRXTYPE','A');
        paramMap.put('TENDER','C');
        paramMap.put('USER',config.PayPal_User__c);
        paramMap.put('VENDOR',config.PayPal_Vender__c);
        paramMap.put('PWD',config.User_credentia__c);
        paramMap.put('PARTNER',config.Partner__c);
        paramMap.put('ACCT', CardNum);
        paramMap.put('EXPDATE', ExpDate);
        if (cvv2 != '' && cvv2 != null) {
            paramMap.put('CVV2', cvv2);
        }
        paramMap.put('AMT', String.valueOf(Amount));
        paramMap.put('COMMENT1',OrderCode);
        paramMap.put('BILLTOFIRSTNAME', billToAddress.BillToFirstName);
        paramMap.put('BILLTOLASTNAME', billToAddress.BillToLastName);
        paramMap.put('BILLTOSTREET', billToAddress.BillToStreet);
        paramMap.put('BILLTOCITY', billToAddress.BillToCity);
        paramMap.put('BILLTOSTATE', billToAddress.BillToState);
        if (billToAddress.BillToCountry != '' && billToAddress.BillToCountry != null) {
            paramMap.put('BILLTOCOUNTRY',billToAddress.BillToCountry);
        }
        paramMap.put('BILLTOZIP', billToAddress.BillToZIP);

        List<String> params = new List<String>();
        for (String key : paramMap.keySet()) {
            params.add(key+'='+paramMap.get(key));
        }

        String body = String.join(params, '&');

        // 创建脱敏的请求体用于日志记录
        String logRequestBody = body.replaceAll('(ACCT=)[^&]*', '$1****')
                                   .replaceAll('(CVV2=)[^&]*', '$1***')
                                   .replaceAll('(PWD=)[^&]*', '$1****');

        String logId = '';
        String respBody = '';
        AuthorizeResponse resp = new AuthorizeResponse();

        try {
            HttpRequest req = new HttpRequest();
            req.setEndpoint(config.End_Point__c);
            req.setMethod('POST');
            req.setBody(body);
            HttpResponse response = new Http().send(req);
            respBody = response.getBody();

            Map<String, String> valMap = new Map<String, String>();
            for (String valPair : respBody.split('&')) {
                List<String> pairs = valPair.split('=');
                if (pairs.size() > 1) {
                    valMap.put(pairs[0], pairs[1]);
                }
            }

            if (valMap.containsKey('RESULT') && valMap.get('RESULT') == '0') {
                resp.resultCode = '0';
                resp.respMsg = valMap.containsKey('RESPMSG') ? valMap.get('RESPMSG') : '';
                resp.PNREF = valMap.containsKey('PNREF') ? valMap.get('PNREF') : '';

                // 保存 Log 对象
                savePayPalLog('Authorize', 'Success', customerName, Amount, OrderCode, resp.PNREF, logId);

            } else {
                resp.resultCode = valMap.containsKey('RESULT') ? valMap.get('RESULT') : '';
                resp.respMsg = valMap.containsKey('RESPMSG') ? valMap.get('RESPMSG') : '';
                resp.PNREF = '';

                // 记录失败日志并保存 Log 对象
                logId = Util.logIntegration(
                    'PayPal Authorize Payment - Failed',
                    'CCM_PayPalUtil',
                    'authorizePayPalPayment',
                    'PayPal authorization failed. Customer: ' + customerName + ', Result Code: ' + resp.resultCode + ', Message: ' + resp.respMsg + ', Time: ' + Datetime.now().format('yyyy-MM-dd HH:mm:ss'),
                    logRequestBody,
                    respBody
                );

                // 保存 Log 对象
                savePayPalLog('Authorize', 'Failed', customerName, Amount, OrderCode, '', logId);
            }

            return resp;
        } catch (Exception e) {
            resp.resultCode = '9999';
            resp.respMsg = e.getMessage();
            resp.PNREF = '';

            // 记录异常日志并保存 Log 对象
            logId = Util.logIntegration(
                'PayPal Authorize Payment - Exception',
                'CCM_PayPalUtil',
                'authorizePayPalPayment',
                'Exception occurred: ' + e.getMessage() + '\nStack Trace: ' + e.getStackTraceString() + ', Customer: ' + customerName + ', Time: ' + Datetime.now().format('yyyy-MM-dd HH:mm:ss'),
                logRequestBody,
                respBody
            );

            // 保存 Log 对象
            savePayPalLog('Authorize', 'Exception', customerName, Amount, OrderCode, '', logId);

            return resp;
        }
    }

    public static CaptureResponse capturePayPalPayment(Decimal amount, String PNREF, String orderNumber) {
        String mdtName = 'PayPal_Sandbox';
        String orgId = UserInfo.getOrganizationId();
        Organization org = [SELECT Id, IsSandbox FROM Organization WHERE Id = :orgId];
        if (!org.IsSandbox) {
            mdtName = 'PayPal_Production';
        }
        PayPal_Configuration__mdt config = [SELECT End_Point__c, PayPal_User__c, PayPal_Vender__c,User_credentia__c,Partner__c
                                           FROM PayPal_Configuration__mdt
                                           WHERE DeveloperName = :mdtName LIMIT 1];

        Map<String, String> paramMap = new Map<String, String>();
        paramMap.put('TRXTYPE','D');
        paramMap.put('TENDER','C');
        paramMap.put('USER',config.PayPal_User__c);
        paramMap.put('VENDOR',config.PayPal_Vender__c);
        paramMap.put('PWD',config.User_credentia__c);
        paramMap.put('PARTNER',config.Partner__c);
        paramMap.put('AMT', String.valueOf(amount));
        paramMap.put('ORIGID',PNREF);
        paramMap.put('CAPTURECOMPLETE','Y');
        paramMap.put('VERBOSITY','HIGH');

        List<String> params = new List<String>();
        for (String key : paramMap.keySet()) {
            params.add(key+'='+paramMap.get(key));
        }
        String body = String.join(params, '&');

        // 创建脱敏的请求体用于日志记录
        String logRequestBody = body.replaceAll('(PWD=)[^&]*', '$1****');

        String logId = '';
        String respBody = '';
        CaptureResponse resp = new CaptureResponse();

        try {
            HttpRequest req = new HttpRequest();
            req.setEndpoint(config.End_Point__c);
            req.setMethod('POST');
            req.setBody(body);
            HttpResponse response = new Http().send(req);
            respBody = response.getBody();

            Map<String, String> valMap = new Map<String, String>();
            for (String valPair : respBody.split('&')) {
                List<String> pairs = valPair.split('=');
                if (pairs.size() > 1) {
                    valMap.put(pairs[0], pairs[1]);
                }
            }

            //check result
            if (valMap.containsKey('RESULT') && valMap.get('RESULT') == '0') {
                resp.resultCode = '0';
                resp.respMsg = valMap.containsKey('RESPMSG') ? valMap.get('RESPMSG') : '';
                resp.PNREF = valMap.containsKey('PNREF') ? valMap.get('PNREF') : '';

                // 记录成功日志并保存 Log 对象
                logId = Util.logIntegration(
                    'PayPal Capture Payment - Success',
                    'CCM_PayPalUtil',
                    'capturePayPalPayment',
                    'PayPal capture successful. Order: ' + orderNumber + ', Amount: ' + amount + ', PNREF: ' + resp.PNREF + ', Time: ' + Datetime.now().format('yyyy-MM-dd HH:mm:ss'),
                    logRequestBody,
                    respBody
                );

                // 保存 Log 对象
                savePayPalLog('Capture', 'Success', '', amount, orderNumber, resp.PNREF, logId);

                system.enqueueJob(new CCM_callPushPayaplToEBSQueue(resp.PNREF,orderNumber,String.valueOf(amount)));

                return resp;
            } else {
                resp.resultCode = valMap.containsKey('RESULT') ? valMap.get('RESULT') : '';
                resp.respMsg = valMap.containsKey('RESPMSG') ? valMap.get('RESPMSG') : '';
                resp.PNREF = '';

                // 记录失败日志并保存 Log 对象
                logId = Util.logIntegration(
                    'PayPal Capture Payment - Failed',
                    'CCM_PayPalUtil',
                    'capturePayPalPayment',
                    'PayPal capture failed. Result Code: ' + resp.resultCode + ', Message: ' + resp.respMsg + ', Order: ' + orderNumber + ', Time: ' + Datetime.now().format('yyyy-MM-dd HH:mm:ss'),
                    logRequestBody,
                    respBody
                );

                // 保存 Log 对象
                savePayPalLog('Capture', 'Failed', '', amount, orderNumber, '', logId);

                return resp;
            }
        } catch (Exception e) {
            resp.resultCode = '9999';
            resp.respMsg = e.getMessage();
            resp.PNREF = '';

            // 记录异常日志并保存 Log 对象
            logId = Util.logIntegration(
                'PayPal Capture Payment - Exception',
                'CCM_PayPalUtil',
                'capturePayPalPayment',
                'Exception occurred: ' + e.getMessage() + '\nStack Trace: ' + e.getStackTraceString() + ', Order: ' + orderNumber + ', Time: ' + Datetime.now().format('yyyy-MM-dd HH:mm:ss'),
                logRequestBody,
                respBody
            );

            // 保存 Log 对象
            savePayPalLog('Capture', 'Exception', '', amount, orderNumber, '', logId);

            return resp;
        }
    }

    public static string syncInvoiceInfoToERP(InvoiceWrap invoiceInfo) {
        //callout interface to ERP or seeburger
        return '';
    }

    public Class CaptureResponse {
        public String resultCode;
        public String respMsg;
        public String PNREF;
    }

    public Class AuthorizeResponse {
        public String resultCode;
        public String respMsg;
        public String PNREF;
    }

    public Class BillingAddress {
        public String BillToFirstName;
        public String BillToLastName;
        public String BillToStreet;
        public String BillToCity;
        public String BillToState;
        public String BillToCountry;
        public String BillToZIP;

        public BillingAddress(String fn, String ln, String street, String city, String state, String country, String zipcode) {
            BillToFirstName = fn;
            BillToLastName = ln;
            BillToStreet = street;
            BillToCity = city;
            BillToState = state;
            BillToCountry = country;
            BillToZIP = zipcode;
        }
    }

    public Class InvoiceWrap {
        public String CustomerId;
        public String OrderId;
        public Decimal Amount;
        public String CurrencyCode;
        public Date IssueDate;
        public String PayFlowTransactionId;
        public BillingAddress BillTo;
    }


    private static void savePayPalLog(String operation, String status, String customerName, Decimal amount, String orderNumber, String pnref, String integrationLogId) {
        try {
            Log__c paypalLog = new Log__c();
            paypalLog.Name = 'PayPal ' + operation + ' - ' + status + ' - ' + Datetime.now().format('yyyy-MM-dd HH:mm:ss');
            paypalLog.ApexName__c = 'CCM_PayPalUtil';
            paypalLog.Method__c = operation.toLowerCase() + 'PayPalPayment';

            // 构建详细的错误消息，包含所有必要信息
            String logMessage = 'PayPal ' + operation + ' Transaction\n';
            logMessage += 'Status: ' + status + '\n';
            logMessage += 'Customer Name: ' + (String.isNotBlank(customerName) ? customerName : 'N/A') + '\n';
            logMessage += 'Amount: $' + (amount != null ? amount.setScale(2) : '0.00') + '\n';
            logMessage += 'Order Number: ' + (String.isNotBlank(orderNumber) ? orderNumber : 'N/A') + '\n';
            logMessage += 'PNREF: ' + (String.isNotBlank(pnref) ? pnref : 'N/A') + '\n';
            logMessage += 'Transaction Time: ' + Datetime.now().format('yyyy-MM-dd HH:mm:ss') + '\n';
            logMessage += 'Integration Log ID: ' + (String.isNotBlank(integrationLogId) ? integrationLogId : 'N/A');

            paypalLog.Error_Message__c = logMessage.left(100000);

            // 构建请求参数信息
            String reqParam = '{\n';
            reqParam += '  "operation": "' + operation + '",\n';
            reqParam += '  "customerName": "' + (String.isNotBlank(customerName) ? customerName : '') + '",\n';
            reqParam += '  "amount": ' + (amount != null ? amount : 0) + ',\n';
            reqParam += '  "orderNumber": "' + (String.isNotBlank(orderNumber) ? orderNumber : '') + '",\n';
            reqParam += '  "timestamp": "' + Datetime.now().format('yyyy-MM-dd HH:mm:ss') + '"\n';
            reqParam += '}';

            paypalLog.ReqParam__c = reqParam.left(100000);

            // 构建响应参数信息
            String resParam = '{\n';
            resParam += '  "status": "' + status + '",\n';
            resParam += '  "pnref": "' + (String.isNotBlank(pnref) ? pnref : '') + '",\n';
            resParam += '  "integrationLogId": "' + (String.isNotBlank(integrationLogId) ? integrationLogId : '') + '",\n';
            resParam += '  "processedTime": "' + Datetime.now().format('yyyy-MM-dd HH:mm:ss') + '"\n';
            resParam += '}';

            paypalLog.ResParam__c = resParam.left(100000);

            // 使用 Database.insert 进行安全插入
            Database.insert(paypalLog, false);

        } catch (Exception e) {
            // 如果保存日志失败，记录到系统调试日志中，避免影响主业务流程
            System.debug(LoggingLevel.ERROR, 'Failed to save PayPal log: ' + e.getMessage());
        }
    }
}